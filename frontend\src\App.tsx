import React, { useState, useEffect } from 'react';
import { Book, BookFormData } from './types';
import { bookAPI } from './services/api';
import BookList from './components/BookList';
import BookForm from './components/BookForm';
import SearchBar from './components/SearchBar';
import { Plus, BookOpen, Loader2 } from 'lucide-react';

function App() {
  const [books, setBooks] = useState<Book[]>([]);
  const [filteredBooks, setFilteredBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingBook, setEditingBook] = useState<Book | undefined>();

  useEffect(() => {
    loadBooks();
  }, []);

  const loadBooks = async () => {
    try {
      setLoading(true);
      const data = await bookAPI.getAll();
      setBooks(data);
      setFilteredBooks(data);
    } catch (err) {
      setError('无法连接到后端服务器。请确保后端服务器正在运行。');
      console.error('Error loading books:', err);
      // Set empty array so the UI still renders
      setBooks([]);
      setFilteredBooks([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddBook = async (bookData: BookFormData) => {
    try {
      const newBook = await bookAPI.create(bookData);
      setBooks(prev => [...prev, newBook]);
      setFilteredBooks(prev => [...prev, newBook]);
    } catch (err) {
      setError('添加图书失败');
      console.error('Error adding book:', err);
    }
  };

  const handleEditBook = async (bookData: BookFormData) => {
    if (!editingBook) return;
    
    try {
      const updatedBook = await bookAPI.update(editingBook._id!, bookData);
      setBooks(prev => prev.map(book => 
        book._id === editingBook._id ? updatedBook : book
      ));
      setFilteredBooks(prev => prev.map(book => 
        book._id === editingBook._id ? updatedBook : book
      ));
      setEditingBook(undefined);
    } catch (err) {
      setError('更新图书失败');
      console.error('Error updating book:', err);
    }
  };

  const handleDeleteBook = async (id: string) => {
    if (!confirm('确定要删除这本图书吗？')) return;
    
    try {
      await bookAPI.delete(id);
      setBooks(prev => prev.filter(book => book._id !== id));
      setFilteredBooks(prev => prev.filter(book => book._id !== id));
    } catch (err) {
      setError('删除图书失败');
      console.error('Error deleting book:', err);
    }
  };

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setFilteredBooks(books);
      return;
    }

    try {
      const results = await bookAPI.search(query);
      setFilteredBooks(results);
    } catch (err) {
      console.error('Search error:', err);
      const localResults = books.filter(book =>
        book.title.toLowerCase().includes(query.toLowerCase()) ||
        book.author.toLowerCase().includes(query.toLowerCase()) ||
        book.isbn.includes(query)
      );
      setFilteredBooks(localResults);
    }
  };

  const handleFilter = (genre: string) => {
    if (!genre) {
      setFilteredBooks(books);
    } else {
      setFilteredBooks(books.filter(book => book.genre === genre));
    }
  };

  const openAddForm = () => {
    setEditingBook(undefined);
    setIsFormOpen(true);
  };

  const openEditForm = (book: Book) => {
    setEditingBook(book);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingBook(undefined);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <BookOpen className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">图书管理系统</h1>
            </div>
            <button
              onClick={openAddForm}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              添加图书
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">{error}</p>
            <button
              onClick={() => setError(null)}
              className="mt-2 text-sm text-red-600 hover:text-red-800"
            >
              关闭
            </button>
          </div>
        )}

        <SearchBar onSearch={handleSearch} onFilter={handleFilter} />
        
        <div className="mb-6">
          <p className="text-sm text-gray-600">
            共找到 {filteredBooks.length} 本图书
          </p>
        </div>

        <BookList
          books={filteredBooks}
          onEdit={openEditForm}
          onDelete={handleDeleteBook}
        />
      </main>

      <BookForm
        book={editingBook}
        isOpen={isFormOpen}
        onClose={closeForm}
        onSubmit={editingBook ? handleEditBook : handleAddBook}
      />
    </div>
  );
}

export default App;
