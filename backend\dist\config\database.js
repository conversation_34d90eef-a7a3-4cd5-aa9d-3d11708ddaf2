"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/bookmanagement';
        await mongoose_1.default.connect(mongoURI);
        console.log('MongoDB 连接成功');
    }
    catch (error) {
        console.error('MongoDB 连接失败:', error);
        process.exit(1);
    }
};
mongoose_1.default.connection.on('disconnected', () => {
    console.log('MongoDB 连接断开');
});
mongoose_1.default.connection.on('error', (error) => {
    console.error('MongoDB 错误:', error);
});
exports.default = connectDB;
//# sourceMappingURL=database.js.map