import { Request, Response } from 'express';
import { Book, IBook } from '../models/Book';

export const getAllBooks = async (req: Request, res: Response): Promise<void> => {
  try {
    const books = await Book.find().sort({ createdAt: -1 });
    res.json(books);
  } catch (error) {
    res.status(500).json({ 
      message: '获取图书列表失败', 
      error: error instanceof Error ? error.message : '未知错误' 
    });
  }
};

export const getBookById = async (req: Request, res: Response): Promise<void> => {
  try {
    const book = await Book.findById(req.params.id);
    if (!book) {
      res.status(404).json({ message: '图书不存在' });
      return;
    }
    res.json(book);
  } catch (error) {
    res.status(500).json({ 
      message: '获取图书详情失败', 
      error: error instanceof Error ? error.message : '未知错误' 
    });
  }
};

export const createBook = async (req: Request, res: Response): Promise<void> => {
  try {
    const bookData = req.body;
    const book = new Book(bookData);
    const savedBook = await book.save();
    res.status(201).json(savedBook);
  } catch (error: any) {
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map((err: any) => err.message);
      res.status(400).json({ message: '数据验证失败', errors });
      return;
    }
    if (error.code === 11000) {
      res.status(400).json({ message: 'ISBN已存在，请使用不同的ISBN' });
      return;
    }
    res.status(500).json({ 
      message: '创建图书失败', 
      error: error instanceof Error ? error.message : '未知错误' 
    });
  }
};

export const updateBook = async (req: Request, res: Response): Promise<void> => {
  try {
    const book = await Book.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!book) {
      res.status(404).json({ message: '图书不存在' });
      return;
    }
    
    res.json(book);
  } catch (error: any) {
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map((err: any) => err.message);
      res.status(400).json({ message: '数据验证失败', errors });
      return;
    }
    if (error.code === 11000) {
      res.status(400).json({ message: 'ISBN已存在，请使用不同的ISBN' });
      return;
    }
    res.status(500).json({ 
      message: '更新图书失败', 
      error: error instanceof Error ? error.message : '未知错误' 
    });
  }
};

export const deleteBook = async (req: Request, res: Response): Promise<void> => {
  try {
    const book = await Book.findByIdAndDelete(req.params.id);
    if (!book) {
      res.status(404).json({ message: '图书不存在' });
      return;
    }
    res.json({ message: '图书删除成功' });
  } catch (error) {
    res.status(500).json({ 
      message: '删除图书失败', 
      error: error instanceof Error ? error.message : '未知错误' 
    });
  }
};

export const searchBooks = async (req: Request, res: Response): Promise<void> => {
  try {
    const { q } = req.query;
    if (!q || typeof q !== 'string') {
      res.status(400).json({ message: '搜索关键词不能为空' });
      return;
    }

    const books = await Book.find({
      $or: [
        { title: { $regex: q, $options: 'i' } },
        { author: { $regex: q, $options: 'i' } },
        { isbn: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } }
      ]
    }).sort({ createdAt: -1 });

    res.json(books);
  } catch (error) {
    res.status(500).json({ 
      message: '搜索图书失败', 
      error: error instanceof Error ? error.message : '未知错误' 
    });
  }
};