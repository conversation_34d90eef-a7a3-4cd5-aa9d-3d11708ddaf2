import { Document } from 'mongoose';
export interface IBook extends Document {
    title: string;
    author: string;
    isbn: string;
    publishYear: number;
    genre: string;
    description: string;
    status: 'available' | 'borrowed';
    createdAt: Date;
    updatedAt: Date;
}
export declare const Book: import("mongoose").Model<IBook, {}, {}, {}, Document<unknown, {}, IBook, {}> & IBook & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Book.d.ts.map