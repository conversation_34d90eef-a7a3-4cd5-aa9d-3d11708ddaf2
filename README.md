# 📚 图书管理系统

一个现代化的图书管理系统，使用 React + TypeScript + Node.js + MongoDB 构建，界面简洁美观，功能完备。

## ✨ 功能特性

- 📖 **图书管理**: 添加、编辑、删除图书信息
- 🔍 **智能搜索**: 支持按标题、作者、ISBN搜索
- 🏷️ **分类筛选**: 按图书类别快速筛选
- 📱 **响应式设计**: 完美适配桌面和移动设备
- 🎨 **现代UI**: 使用 Tailwind CSS 打造简洁美观的界面
- ⚡ **实时更新**: 增删改查实时同步

## 🛠️ 技术栈

### 前端
- **React 18** - 现代化的用户界面库
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速的构建工具
- **Tailwind CSS** - 实用优先的CSS框架
- **Lucide React** - 现代化的图标库
- **Axios** - HTTP客户端

### 后端
- **Node.js** - JavaScript运行时
- **Express** - Web应用框架
- **TypeScript** - 类型安全的JavaScript
- **MongoDB** - NoSQL数据库
- **Mongoose** - MongoDB对象建模工具

## 📋 系统要求

- Node.js >= 16.0.0
- MongoDB >= 4.4.0
- npm >= 8.0.0

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd book-management-system
```

### 2. 安装依赖

```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install
```

### 3. 启动MongoDB

确保MongoDB服务正在运行：

```bash
# Windows (如果安装为服务)
net start MongoDB

# macOS (使用Homebrew)
brew services start mongodb/brew/mongodb-community

# Linux (systemd)
sudo systemctl start mongod
```

### 4. 配置环境变量

后端环境变量已在 `backend/.env` 文件中预配置，如需修改请编辑该文件：

```env
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/bookmanagement
FRONTEND_URL=http://localhost:5173
```

### 5. 启动应用

```bash
# 启动后端API (在backend目录下)
npm run dev

# 启动前端应用 (在frontend目录下，新开终端)
npm run dev
```

### 6. 访问应用

- 前端应用: http://localhost:5173
- 后端API: http://localhost:3001
- API健康检查: http://localhost:3001/api/health

## 📚 API文档

### 图书管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/books` | 获取所有图书 |
| GET | `/api/books/:id` | 获取单本图书详情 |
| POST | `/api/books` | 创建新图书 |
| PUT | `/api/books/:id` | 更新图书信息 |
| DELETE | `/api/books/:id` | 删除图书 |
| GET | `/api/books/search?q=关键词` | 搜索图书 |

### 图书数据模型

```typescript
interface Book {
  _id?: string;
  title: string;        // 图书标题
  author: string;       // 作者
  isbn: string;         // ISBN (唯一)
  publishYear: number;  // 出版年份
  genre: string;        // 类别
  description: string;  // 简介
  status: 'available' | 'borrowed'; // 状态
  createdAt?: Date;     // 创建时间
  updatedAt?: Date;     // 更新时间
}
```

## 🎨 界面预览

系统采用现代化的设计风格：

- **简洁清爽**: 使用浅色背景和卡片式布局
- **响应式设计**: 自适应各种屏幕尺寸
- **直观操作**: 清晰的按钮和表单设计
- **状态反馈**: 实时的加载和错误提示

## 🔧 开发说明

### 项目结构

```
book-management-system/
├── frontend/                 # 前端应用
│   ├── src/
│   │   ├── components/      # React组件
│   │   ├── services/        # API服务
│   │   ├── types/           # TypeScript类型定义
│   │   └── ...
│   ├── public/
│   └── package.json
├── backend/                  # 后端API
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由
│   │   ├── config/          # 配置文件
│   │   └── ...
│   └── package.json
└── README.md
```

### 构建生产环境

```bash
# 构建前端
cd frontend
npm run build

# 构建后端
cd ../backend
npm run build

# 启动生产环境
npm start
```

## 🤝 贡献指南

1. Fork 这个项目
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 📝 更新日志

### v1.0.0 (2025-07-30)
- ✨ 初始版本发布
- 📚 完整的图书CRUD功能
- 🔍 搜索和筛选功能
- 🎨 现代化的用户界面
- 📱 响应式设计

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下开源项目：

- [React](https://reactjs.org/)
- [Express](https://expressjs.com/)
- [MongoDB](https://www.mongodb.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Lucide](https://lucide.dev/)

---

💡 如有问题或建议，欢迎提交 Issue 或 Pull Request！