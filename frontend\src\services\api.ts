import axios from 'axios';
import { Book, BookFormData } from '../types';

const API_BASE_URL = 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const bookAPI = {
  getAll: async (): Promise<Book[]> => {
    const response = await api.get('/books');
    return response.data;
  },

  getById: async (id: string): Promise<Book> => {
    const response = await api.get(`/books/${id}`);
    return response.data;
  },

  create: async (book: BookFormData): Promise<Book> => {
    const response = await api.post('/books', book);
    return response.data;
  },

  update: async (id: string, book: Partial<BookFormData>): Promise<Book> => {
    const response = await api.put(`/books/${id}`, book);
    return response.data;
  },

  delete: async (id: string): Promise<void> => {
    await api.delete(`/books/${id}`);
  },

  search: async (query: string): Promise<Book[]> => {
    const response = await api.get(`/books/search?q=${encodeURIComponent(query)}`);
    return response.data;
  },
};