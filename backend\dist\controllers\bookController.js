"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchBooks = exports.deleteBook = exports.updateBook = exports.createBook = exports.getBookById = exports.getAllBooks = void 0;
const Book_1 = require("../models/Book");
const getAllBooks = async (req, res) => {
    try {
        const books = await Book_1.Book.find().sort({ createdAt: -1 });
        res.json(books);
    }
    catch (error) {
        res.status(500).json({
            message: '获取图书列表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.getAllBooks = getAllBooks;
const getBookById = async (req, res) => {
    try {
        const book = await Book_1.Book.findById(req.params.id);
        if (!book) {
            res.status(404).json({ message: '图书不存在' });
            return;
        }
        res.json(book);
    }
    catch (error) {
        res.status(500).json({
            message: '获取图书详情失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.getBookById = getBookById;
const createBook = async (req, res) => {
    try {
        const bookData = req.body;
        const book = new Book_1.Book(bookData);
        const savedBook = await book.save();
        res.status(201).json(savedBook);
    }
    catch (error) {
        if (error.name === 'ValidationError') {
            const errors = Object.values(error.errors).map((err) => err.message);
            res.status(400).json({ message: '数据验证失败', errors });
            return;
        }
        if (error.code === 11000) {
            res.status(400).json({ message: 'ISBN已存在，请使用不同的ISBN' });
            return;
        }
        res.status(500).json({
            message: '创建图书失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.createBook = createBook;
const updateBook = async (req, res) => {
    try {
        const book = await Book_1.Book.findByIdAndUpdate(req.params.id, req.body, { new: true, runValidators: true });
        if (!book) {
            res.status(404).json({ message: '图书不存在' });
            return;
        }
        res.json(book);
    }
    catch (error) {
        if (error.name === 'ValidationError') {
            const errors = Object.values(error.errors).map((err) => err.message);
            res.status(400).json({ message: '数据验证失败', errors });
            return;
        }
        if (error.code === 11000) {
            res.status(400).json({ message: 'ISBN已存在，请使用不同的ISBN' });
            return;
        }
        res.status(500).json({
            message: '更新图书失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.updateBook = updateBook;
const deleteBook = async (req, res) => {
    try {
        const book = await Book_1.Book.findByIdAndDelete(req.params.id);
        if (!book) {
            res.status(404).json({ message: '图书不存在' });
            return;
        }
        res.json({ message: '图书删除成功' });
    }
    catch (error) {
        res.status(500).json({
            message: '删除图书失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.deleteBook = deleteBook;
const searchBooks = async (req, res) => {
    try {
        const { q } = req.query;
        if (!q || typeof q !== 'string') {
            res.status(400).json({ message: '搜索关键词不能为空' });
            return;
        }
        const books = await Book_1.Book.find({
            $or: [
                { title: { $regex: q, $options: 'i' } },
                { author: { $regex: q, $options: 'i' } },
                { isbn: { $regex: q, $options: 'i' } },
                { description: { $regex: q, $options: 'i' } }
            ]
        }).sort({ createdAt: -1 });
        res.json(books);
    }
    catch (error) {
        res.status(500).json({
            message: '搜索图书失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.searchBooks = searchBooks;
//# sourceMappingURL=bookController.js.map