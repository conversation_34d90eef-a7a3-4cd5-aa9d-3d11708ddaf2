import { Schema, model, Document } from 'mongoose';

export interface IBook extends Document {
  title: string;
  author: string;
  isbn: string;
  publishYear: number;
  genre: string;
  description: string;
  status: 'available' | 'borrowed';
  createdAt: Date;
  updatedAt: Date;
}

const bookSchema = new Schema<IBook>({
  title: {
    type: String,
    required: [true, '图书标题不能为空'],
    trim: true,
    maxlength: [200, '图书标题不能超过200个字符']
  },
  author: {
    type: String,
    required: [true, '作者不能为空'],
    trim: true,
    maxlength: [100, '作者姓名不能超过100个字符']
  },
  isbn: {
    type: String,
    required: [true, 'ISBN不能为空'],
    unique: true,
    trim: true,
    validate: {
      validator: function(v: string) {
        return /^(?:ISBN(?:-1[03])?:? )?((?:97[89])?\d{9}[\dx])$/i.test(v.replace(/[^\dX]/gi, ''));
      },
      message: '请输入有效的ISBN格式'
    }
  },
  publishYear: {
    type: Number,
    required: [true, '出版年份不能为空'],
    min: [1000, '出版年份不能早于1000年'],
    max: [new Date().getFullYear() + 1, '出版年份不能超过明年']
  },
  genre: {
    type: String,
    required: [true, '图书类别不能为空'],
    enum: {
      values: ['文学', '科学', '历史', '哲学', '艺术', '技术', '教育', '医学', '法律', '经济', '管理', '心理学', '社会学', '其他'],
      message: '请选择有效的图书类别'
    }
  },
  description: {
    type: String,
    maxlength: [1000, '图书简介不能超过1000个字符'],
    default: ''
  },
  status: {
    type: String,
    enum: ['available', 'borrowed'],
    default: 'available'
  }
}, {
  timestamps: true
});

bookSchema.index({ title: 'text', author: 'text', description: 'text' });
bookSchema.index({ genre: 1 });
bookSchema.index({ status: 1 });

export const Book = model<IBook>('Book', bookSchema);